"""
Audit logging utilities for the supply management system.
"""
from django.contrib.auth.models import User
from .models import AuditLog


def log_audit_event(user, action_type, obj, changes=None, request=None, additional_data=None):
    """
    Log an audit event.
    
    Args:
        user: User who performed the action (can be None for system actions)
        action_type: Type of action (from AuditLog.ACTION_TYPES)
        obj: The object that was acted upon
        changes: Dictionary of field changes (old_value -> new_value)
        request: HTTP request object (for IP and user agent)
        additional_data: Any additional context data
    """
    # Get IP address and user agent from request
    ip_address = None
    user_agent = ''
    
    if request:
        # Get IP address
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
        
        # Get user agent
        user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    # Create audit log entry
    AuditLog.objects.create(
        user=user,
        action_type=action_type,
        object_type=obj.__class__.__name__,
        object_id=str(obj.pk) if hasattr(obj, 'pk') else None,
        object_repr=str(obj),
        changes=changes or {},
        ip_address=ip_address,
        user_agent=user_agent,
        additional_data=additional_data or {}
    )


def log_request_action(user, action_type, supply_request, request=None, additional_data=None):
    """
    Convenience function for logging supply request actions.
    """
    log_audit_event(
        user=user,
        action_type=action_type,
        obj=supply_request,
        request=request,
        additional_data=additional_data
    )


def log_inventory_action(user, action_type, supply_item, changes=None, request=None, additional_data=None):
    """
    Convenience function for logging inventory actions.
    """
    log_audit_event(
        user=user,
        action_type=action_type,
        obj=supply_item,
        changes=changes,
        request=request,
        additional_data=additional_data
    )


def log_bulk_operation(user, action_type, objects, request=None, additional_data=None):
    """
    Log a bulk operation affecting multiple objects.
    """
    object_ids = [str(obj.pk) for obj in objects if hasattr(obj, 'pk')]
    object_reprs = [str(obj) for obj in objects]
    
    # Create a single audit log entry for the bulk operation
    log_audit_event(
        user=user,
        action_type='BULK_OPERATION',
        obj=objects[0] if objects else None,  # Use first object as reference
        request=request,
        additional_data={
            'bulk_action_type': action_type,
            'affected_objects': object_ids,
            'object_representations': object_reprs,
            'count': len(objects),
            **(additional_data or {})
        }
    )


def log_user_action(user, action_type, request=None):
    """
    Log user authentication actions (login, logout).
    """
    # Create a dummy object for user actions
    class UserAction:
        def __init__(self, username):
            self.username = username
            self.pk = username
        
        def __str__(self):
            return f"User: {self.username}"
        
        class __class__:
            __name__ = 'User'
    
    user_obj = UserAction(user.username if user else 'Anonymous')
    
    log_audit_event(
        user=user,
        action_type=action_type,
        obj=user_obj,
        request=request
    )


def get_audit_trail_for_object(obj, limit=50):
    """
    Get audit trail for a specific object.
    
    Args:
        obj: The object to get audit trail for
        limit: Maximum number of entries to return
    
    Returns:
        QuerySet of AuditLog entries
    """
    return AuditLog.objects.filter(
        object_type=obj.__class__.__name__,
        object_id=str(obj.pk)
    ).select_related('user')[:limit]


def get_user_activity(user, limit=100):
    """
    Get recent activity for a specific user.
    
    Args:
        user: User to get activity for
        limit: Maximum number of entries to return
    
    Returns:
        QuerySet of AuditLog entries
    """
    return AuditLog.objects.filter(
        user=user
    ).select_related('user')[:limit]


def get_system_activity(limit=200):
    """
    Get recent system-wide activity.
    
    Args:
        limit: Maximum number of entries to return
    
    Returns:
        QuerySet of AuditLog entries
    """
    return AuditLog.objects.all().select_related('user')[:limit]


# Decorator for automatically logging view actions
def audit_action(action_type, get_object=None):
    """
    Decorator to automatically log audit events for view actions.
    
    Args:
        action_type: The type of action being performed
        get_object: Function to extract the object from view args/kwargs
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            # Execute the view
            response = view_func(request, *args, **kwargs)
            
            # Log the action if successful (status code < 400)
            if hasattr(response, 'status_code') and response.status_code < 400:
                try:
                    if get_object:
                        obj = get_object(*args, **kwargs)
                        log_audit_event(
                            user=request.user if request.user.is_authenticated else None,
                            action_type=action_type,
                            obj=obj,
                            request=request
                        )
                except Exception as e:
                    # Don't let audit logging break the application
                    print(f"Audit logging error: {e}")
            
            return response
        return wrapper
    return decorator
