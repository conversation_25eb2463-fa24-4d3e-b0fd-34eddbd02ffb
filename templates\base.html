<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MSRRMS - Municipal Supply Request and Release Management System{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3/unpoly.min.css">
    
    <!-- Custom styles -->
    <style>
        [x-cloak] { display: none !important; }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-blue-600 shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-white text-xl font-bold">MSRRMS</h1>
                    </div>
                    {% if user.is_authenticated %}
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="{% url 'supply:dashboard' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                            {% if user.userprofile.role == 'GSO' %}
                            <a href="{% url 'supply:gso_dashboard' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">GSO Dashboard</a>
                            <a href="{% url 'supply:inventory' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Inventory</a>
                            <a href="{% url 'supply:reports' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Reports</a>
                            {% else %}
                            <a href="{% url 'supply:request_create' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">New Request</a>
                            <a href="{% url 'supply:request_history' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">My Requests</a>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                {% if user.is_authenticated %}
                <div class="flex items-center space-x-4">
                    <div class="text-white text-sm">
                        Welcome, {{ user.first_name|default:user.username }}
                        {% if user.userprofile %}
                        <span class="text-blue-200">({{ user.userprofile.get_role_display }})</span>
                        {% endif %}
                    </div>
                    <a href="{% url 'supply:profile' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Profile</a>
                    <a href="{% url 'logout' %}" class="bg-blue-700 hover:bg-blue-800 text-white px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
                {% else %}
                <div class="flex items-center space-x-2">
                    <a href="{% url 'login' %}" class="text-white hover:bg-blue-700 px-3 py-2 rounded-md text-sm font-medium">Login</a>
                    <a href="{% url 'register' %}" class="bg-blue-700 hover:bg-blue-800 text-white px-3 py-2 rounded-md text-sm font-medium">Register</a>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
        {% for message in messages %}
        {% if message.tags == 'error' %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
            {{ message }}
        </div>
        {% elif message.tags == 'success' %}
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4" role="alert">
            {{ message }}
        </div>
        {% else %}
        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4" role="alert">
            {{ message }}
        </div>
        {% endif %}
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-auto">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="text-center text-sm">
                <p>&copy; 2024 Municipal Supply Request and Release Management System. All rights reserved.</p>
            </div>
        </div>
    </footer>

    {% block extra_scripts %}{% endblock %}
</body>
</html>