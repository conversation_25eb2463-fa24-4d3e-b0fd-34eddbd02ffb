{% extends 'base.html' %}

{% block title %}Register - MSRRMS{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Join the Municipal Supply Request and Release Management System
            </p>
        </div>
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            
            <div class="space-y-4">
                <!-- Username -->
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Username
                    </label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.username.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- First Name -->
                <div>
                    <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        First Name
                    </label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.first_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Last Name -->
                <div>
                    <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Last Name
                    </label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.last_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Email -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Email Address
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.email.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Role -->
                <div>
                    <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Role
                    </label>
                    {{ form.role }}
                    {% if form.role.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.role.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Department -->
                <div>
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Department
                    </label>
                    {{ form.department }}
                    {% if form.department.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.department.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Phone -->
                <div>
                    <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Phone Number (Optional)
                    </label>
                    {{ form.phone }}
                    {% if form.phone.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.phone.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Password -->
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Password
                    </label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.password1.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Confirm Password
                    </label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.password2.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {{ form.non_field_errors }}
            </div>
            {% endif %}

            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Create Account
                </button>
            </div>

            <div class="text-center">
                <p class="text-sm text-gray-600">
                    Already have an account? 
                    <a href="{% url 'login' %}" class="font-medium text-blue-600 hover:text-blue-500">
                        Sign in here
                    </a>
                </p>
            </div>
        </form>
    </div>
</div>
{% endblock %}