# Generated by Django 4.2.17 on 2025-07-21 17:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('supply', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('CREATE', 'Create'), ('UPDATE', 'Update'), ('DELETE', 'Delete'), ('APPROVE', 'Approve'), ('REJECT', 'Reject'), ('RELEASE', 'Release'), ('LOGIN', 'Login'), ('LOGOUT', 'Logout'), ('INVENTORY_ADJUST', 'Inventory Adjustment'), ('BULK_OPERATION', 'Bulk Operation')], max_length=20)),
                ('object_type', models.CharField(max_length=50)),
                ('object_id', models.CharField(blank=True, max_length=50, null=True)),
                ('object_repr', models.CharField(max_length=200)),
                ('changes', models.TextField(blank=True, default='{}')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('additional_data', models.TextField(blank=True, default='{}')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='supply_audi_user_id_98a5c5_idx'), models.Index(fields=['action_type', 'timestamp'], name='supply_audi_action__6a3791_idx'), models.Index(fields=['object_type', 'object_id'], name='supply_audi_object__4c99ae_idx')],
            },
        ),
    ]
