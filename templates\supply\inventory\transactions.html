{% extends 'base.html' %}

{% block title %}Inventory Transactions{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <a href="{% url 'supply:inventory' %}" 
               class="text-blue-600 hover:text-blue-800 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-gray-900">Inventory Transactions</h1>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <form method="get" class="flex flex-wrap gap-4 items-end">
            <div class="flex-1 min-w-64">
                <label for="item" class="block text-sm font-medium text-gray-700 mb-2">Filter by Item</label>
                <select name="item" 
                        id="item"
                        class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">All Items</option>
                    {% for item in items %}
                    <option value="{{ item.id }}" {% if selected_item == item.id|stringformat:"s" %}selected{% endif %}>
                        {{ item.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="min-w-48">
                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Transaction Type</label>
                <select name="type" 
                        id="type"
                        class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">All Types</option>
                    {% for value, label in transaction_types %}
                    <option value="{{ value }}" {% if selected_type == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Filter
            </button>
            
            {% if selected_item or selected_type %}
            <a href="{% url 'supply:inventory_transactions' %}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Clear
            </a>
            {% endif %}
        </form>
    </div>

    <!-- Transactions List -->
    {% if page_obj %}
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul class="divide-y divide-gray-200">
            {% for transaction in page_obj %}
            <li>
                <div class="px-4 py-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            {% if transaction.transaction_type == 'IN' %}
                            <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </span>
                            {% elif transaction.transaction_type == 'OUT' %}
                            <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
                                </svg>
                            </span>
                            {% else %}
                            <span class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </span>
                            {% endif %}
                        </div>
                        
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">
                                        <a href="{% url 'supply:inventory_detail' transaction.item.id %}" class="hover:text-blue-600">
                                            {{ transaction.item.name }}
                                        </a>
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                              {% if transaction.transaction_type == 'IN' %}bg-green-100 text-green-800
                                              {% elif transaction.transaction_type == 'OUT' %}bg-red-100 text-red-800
                                              {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                            {{ transaction.get_transaction_type_display }}
                                        </span>
                                        <span class="ml-2">{{ transaction.quantity }} {{ transaction.item.unit }}</span>
                                        {% if transaction.reference_request %}
                                        <span class="ml-2">for request {{ transaction.reference_request.request_id }}</span>
                                        {% endif %}
                                    </p>
                                </div>
                                
                                <div class="text-right">
                                    <p class="text-sm text-gray-900">
                                        {{ transaction.created_at|date:"M d, Y" }}
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        {{ transaction.created_at|time:"g:i A" }}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <p class="text-sm text-gray-600">
                                    By {{ transaction.performed_by.get_full_name|default:transaction.performed_by.username }}
                                </p>
                                {% if transaction.remarks %}
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-medium">Remarks:</span> {{ transaction.remarks }}
                                </p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if selected_item %}&item={{ selected_item }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
                {% endif %}
                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if selected_item %}&item={{ selected_item }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ page_obj.start_index }}</span>
                        to
                        <span class="font-medium">{{ page_obj.end_index }}</span>
                        of
                        <span class="font-medium">{{ page_obj.paginator.count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if selected_item %}&item={{ selected_item }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Previous
                        </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if selected_item %}&item={{ selected_item }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if selected_item %}&item={{ selected_item }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Next
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% else %}
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
        <p class="mt-1 text-sm text-gray-500">
            {% if selected_item or selected_type %}
            No transactions match your current filters.
            {% else %}
            No inventory transactions have been recorded yet.
            {% endif %}
        </p>
        <div class="mt-6">
            {% if selected_item or selected_type %}
            <a href="{% url 'supply:inventory_transactions' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Clear Filters
            </a>
            {% else %}
            <a href="{% url 'supply:inventory' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                View Inventory
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}