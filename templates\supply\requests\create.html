{% extends 'base.html' %}

{% block title %}Create Supply Request - MSRRMS{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-3xl mx-auto">
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Create Supply Request</h1>
                    <p class="mt-1 text-sm text-gray-600">Submit a new request for supplies needed by your department.</p>
                </div>

                <form method="post" hx-post="{% url 'supply:request_create' %}" hx-target="#form-container" hx-swap="outerHTML">
                    <div id="form-container">
                        {% csrf_token %}
                        
                        <div class="space-y-6">
                            <!-- Item Selection -->
                            <div>
                                <label for="{{ form.item.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Supply Item <span class="text-red-500">*</span>
                                </label>
                                {{ form.item }}
                                {% if form.item.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.item.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Item Information Display -->
                            <div id="item-info" class="hidden">
                                <!-- Will be populated by HTMX -->
                            </div>

                            <!-- Quantity -->
                            <div>
                                <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Quantity <span class="text-red-500">*</span>
                                </label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.quantity.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Purpose -->
                            <div>
                                <label for="{{ form.purpose.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Purpose <span class="text-red-500">*</span>
                                </label>
                                {{ form.purpose }}
                                <p class="mt-1 text-sm text-gray-500">Describe why you need these supplies (minimum 10 characters).</p>
                                {% if form.purpose.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.purpose.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Department Info -->
                            {% if user.userprofile.department %}
                            <div class="bg-gray-50 p-4 rounded-md">
                                <h3 class="text-sm font-medium text-gray-700">Request Information</h3>
                                <div class="mt-2 text-sm text-gray-600">
                                    <p><strong>Department:</strong> {{ user.userprofile.department }}</p>
                                    <p><strong>Requested by:</strong> {{ user.get_full_name|default:user.username }}</p>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="{% url 'supply:request_history' %}" 
                               class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Submit Request
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide item info based on selection
    const itemSelect = document.getElementById('{{ form.item.id_for_label }}');
    const itemInfo = document.getElementById('item-info');
    
    if (itemSelect && itemInfo) {
        itemSelect.addEventListener('change', function() {
            if (this.value) {
                itemInfo.classList.remove('hidden');
            } else {
                itemInfo.classList.add('hidden');
            }
        });
    }
});
</script>
{% endblock %}