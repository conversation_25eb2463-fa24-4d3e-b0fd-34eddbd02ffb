{% extends 'base.html' %}

{% block title %}Create Supply Request - MSRRMS{% endblock %}

{% block content %}
<div class="px-4 sm:px-0" x-data="requestForm()">
    <div class="max-w-3xl mx-auto">
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Create Supply Request</h1>
                    <p class="mt-1 text-sm text-gray-600">Submit a new request for supplies needed by your department.</p>
                </div>

                <form method="post"
                      hx-post="{% url 'supply:request_create' %}"
                      hx-target="#form-container"
                      hx-swap="outerHTML"
                      hx-indicator="#form-loading"
                      @submit="handleSubmit">
                    <div id="form-container">
                        {% csrf_token %}
                        
                        <div class="space-y-6">
                            <!-- Item Selection -->
                            <div>
                                <label for="{{ form.item.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Supply Item <span class="text-red-500">*</span>
                                </label>
                                {{ form.item }}
                                {% if form.item.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.item.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Item Information Display -->
                            <div id="item-info" class="hidden">
                                <!-- Will be populated by HTMX -->
                            </div>

                            <!-- Quantity -->
                            <div>
                                <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Quantity <span class="text-red-500">*</span>
                                </label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.quantity.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Purpose -->
                            <div>
                                <label for="{{ form.purpose.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Purpose <span class="text-red-500">*</span>
                                </label>
                                {{ form.purpose }}
                                <p class="mt-1 text-sm text-gray-500">Describe why you need these supplies (minimum 10 characters).</p>
                                {% if form.purpose.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ form.purpose.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Department Info -->
                            {% if user.userprofile.department %}
                            <div class="bg-gray-50 p-4 rounded-md">
                                <h3 class="text-sm font-medium text-gray-700">Request Information</h3>
                                <div class="mt-2 text-sm text-gray-600">
                                    <p><strong>Department:</strong> {{ user.userprofile.department }}</p>
                                    <p><strong>Requested by:</strong> {{ user.get_full_name|default:user.username }}</p>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="mt-6 flex justify-end space-x-3">
                            <a href="{% url 'supply:request_history' %}"
                               class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all">
                                Cancel
                            </a>
                            <button type="submit"
                                    :disabled="isSubmitting"
                                    :class="isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'"
                                    class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all flex items-center">
                                <svg x-show="isSubmitting" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span x-text="isSubmitting ? 'Submitting...' : 'Submit Request'"></span>
                            </button>

                            <!-- Loading indicator for HTMX -->
                            <div id="form-loading" class="htmx-indicator flex items-center text-blue-600">
                                <svg class="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function requestForm() {
    return {
        selectedItem: '',
        isSubmitting: false,

        updateItemInfo() {
            const itemInfo = document.getElementById('item-info');
            if (this.selectedItem) {
                itemInfo.classList.remove('hidden');
            } else {
                itemInfo.classList.add('hidden');
            }
        },

        handleSubmit() {
            this.isSubmitting = true;
            // Show loading state
            showNotification('info', 'Processing', 'Submitting your request...');
        },

        validateQuantity(event) {
            const value = parseInt(event.target.value);
            const selectedOption = document.querySelector(`#{{ form.item.id_for_label }} option[value="${this.selectedItem}"]`);

            if (selectedOption) {
                const availableStock = parseInt(selectedOption.dataset.stock);
                if (value > availableStock) {
                    showNotification('warning', 'Quantity Warning', `Requested quantity (${value}) exceeds available stock (${availableStock})`);
                }
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Enhanced form interactions
    const itemSelect = document.getElementById('{{ form.item.id_for_label }}');
    const quantityInput = document.getElementById('{{ form.quantity.id_for_label }}');

    // Add HTMX attributes to item select if not already present
    if (itemSelect && !itemSelect.hasAttribute('hx-get')) {
        itemSelect.setAttribute('hx-get', '{% url "supply:item_info" %}');
        itemSelect.setAttribute('hx-target', '#item-info');
        itemSelect.setAttribute('hx-trigger', 'change');
    }

    // Add real-time quantity validation
    if (quantityInput) {
        quantityInput.addEventListener('input', function(event) {
            const value = parseInt(event.target.value);
            const selectedOption = itemSelect.querySelector('option:checked');

            if (selectedOption && selectedOption.value) {
                const availableStock = parseInt(selectedOption.textContent.match(/\((\d+)/)?.[1] || 0);
                const quantityGroup = quantityInput.closest('div');
                let warningElement = quantityGroup.querySelector('.quantity-warning');

                if (value > availableStock) {
                    if (!warningElement) {
                        warningElement = document.createElement('div');
                        warningElement.className = 'quantity-warning mt-1 text-sm text-yellow-600';
                        quantityGroup.appendChild(warningElement);
                    }
                    warningElement.textContent = `Warning: Requested quantity (${value}) exceeds available stock (${availableStock})`;
                } else if (warningElement) {
                    warningElement.remove();
                }
            }
        });
    }

    // Form submission success handler
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 200 && event.target.closest('form')) {
            showNotification('success', 'Request Submitted', 'Your supply request has been submitted successfully.');
        }
    });
});
</script>
{% endblock %}