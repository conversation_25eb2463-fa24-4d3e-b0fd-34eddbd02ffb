{% extends 'base.html' %}

{% block title %}GSO Dashboard - MSRRMS{% endblock %}

{% block content %}
<div class="px-4 sm:px-0" x-data="gsoDashboard()">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">GSO Dashboard</h1>
            <p class="mt-1 text-lg text-gray-600">Manage supply requests and approvals</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ total_requests }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                                <dd class="text-lg font-medium text-yellow-600">{{ pending_requests }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Approved</dt>
                                <dd class="text-lg font-medium text-blue-600">{{ approved_requests }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Released</dt>
                                <dd class="text-lg font-medium text-green-600">{{ released_requests }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Rejected</dt>
                                <dd class="text-lg font-medium text-red-600">{{ rejected_requests }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Controls -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <form hx-get="{% url 'supply:gso_dashboard' %}" 
                      hx-target="#request-list" 
                      hx-trigger="change, submit"
                      hx-indicator="#loading-indicator"
                      class="space-y-4 sm:space-y-0 sm:flex sm:items-center sm:space-x-4">
                    
                    <!-- Status Filter -->
                    <div class="flex-1 min-w-0">
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" id="status" 
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option value="">All Statuses</option>
                            {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Department Filter -->
                    <div class="flex-1 min-w-0">
                        <label for="department" class="block text-sm font-medium text-gray-700">Department</label>
                        <select name="department" id="department" 
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option value="">All Departments</option>
                            {% for dept in departments %}
                            <option value="{{ dept }}" {% if department_filter == dept %}selected{% endif %}>{{ dept }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Search -->
                    <div class="flex-1 min-w-0">
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" name="search" id="search" value="{{ search_query }}"
                               placeholder="Request ID, item name, purpose..."
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>

                    <!-- Sort -->
                    <div class="flex-1 min-w-0">
                        <label for="sort" class="block text-sm font-medium text-gray-700">Sort By</label>
                        <select name="sort" id="sort" 
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>Newest First</option>
                            <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Oldest First</option>
                            <option value="status" {% if sort_by == 'status' %}selected{% endif %}>Status A-Z</option>
                            <option value="-status" {% if sort_by == '-status' %}selected{% endif %}>Status Z-A</option>
                            <option value="department" {% if sort_by == 'department' %}selected{% endif %}>Department A-Z</option>
                            <option value="-department" {% if sort_by == '-department' %}selected{% endif %}>Department Z-A</option>
                            <option value="item__name" {% if sort_by == 'item__name' %}selected{% endif %}>Item A-Z</option>
                            <option value="-item__name" {% if sort_by == '-item__name' %}selected{% endif %}>Item Z-A</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>

        <!-- Batch Operations -->
        <div class="bg-white shadow rounded-lg mb-6" x-show="selectedRequests.length > 0" x-cloak>
            <div class="px-4 py-3 sm:px-6 bg-gray-50 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-900" x-text="`${selectedRequests.length} request(s) selected`"></span>
                    </div>
                    <div class="flex space-x-2">
                        <button type="button" 
                                @click="bulkApprove()"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Bulk Approve
                        </button>
                        <button type="button" 
                                @click="bulkReject()"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            Bulk Reject
                        </button>
                        <button type="button" 
                                @click="clearSelection()"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Clear Selection
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loading-indicator" class="htmx-indicator">
            <div class="flex justify-center items-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span class="ml-2 text-gray-600">Loading...</span>
            </div>
        </div>

        <!-- Request List -->
        <div id="request-list" 
             hx-get="{% url 'supply:gso_dashboard' %}?status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
             hx-trigger="every 30s"
             hx-swap="innerHTML">
            {% include 'supply/gso/dashboard_list.html' %}
        </div>

        <!-- Bulk Action Modals -->
        <div x-show="showBulkApproveModal" 
             x-cloak
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click.away="showBulkApproveModal = false">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3 text-center">
                    <h3 class="text-lg font-medium text-gray-900">Confirm Bulk Approval</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500" x-text="`Are you sure you want to approve ${selectedRequests.length} request(s)?`"></p>
                    </div>
                    <div class="flex justify-center space-x-4 px-4 py-3">
                        <button @click="confirmBulkApprove()" 
                                class="px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                            Confirm
                        </button>
                        <button @click="showBulkApproveModal = false" 
                                class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div x-show="showBulkRejectModal" 
             x-cloak
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click.away="showBulkRejectModal = false">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 text-center">Confirm Bulk Rejection</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500 text-center" x-text="`Are you sure you want to reject ${selectedRequests.length} request(s)?`"></p>
                        <div class="mt-4">
                            <label for="rejection_reason" class="block text-sm font-medium text-gray-700">Reason for rejection:</label>
                            <textarea x-model="rejectionReason" 
                                      id="rejection_reason"
                                      rows="3" 
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                      placeholder="Enter reason for rejection..."></textarea>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-4 px-4 py-3">
                        <button @click="confirmBulkReject()" 
                                class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                            Confirm
                        </button>
                        <button @click="showBulkRejectModal = false" 
                                class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function gsoDashboard() {
    return {
        selectedRequests: [],
        showBulkApproveModal: false,
        showBulkRejectModal: false,
        rejectionReason: '',
        
        toggleRequest(requestId) {
            const index = this.selectedRequests.indexOf(requestId);
            if (index > -1) {
                this.selectedRequests.splice(index, 1);
            } else {
                this.selectedRequests.push(requestId);
            }
        },
        
        toggleAll(checked) {
            if (checked) {
                // Select all visible requests
                const checkboxes = document.querySelectorAll('input[name="request_checkbox"]');
                this.selectedRequests = Array.from(checkboxes).map(cb => parseInt(cb.value));
            } else {
                this.selectedRequests = [];
            }
        },
        
        isSelected(requestId) {
            return this.selectedRequests.includes(requestId);
        },
        
        clearSelection() {
            this.selectedRequests = [];
        },
        
        bulkApprove() {
            this.showBulkApproveModal = true;
        },
        
        bulkReject() {
            this.showBulkRejectModal = true;
            this.rejectionReason = '';
        },
        
        confirmBulkApprove() {
            this.performBulkAction('bulk_approve');
            this.showBulkApproveModal = false;
        },
        
        confirmBulkReject() {
            this.performBulkAction('bulk_reject');
            this.showBulkRejectModal = false;
        },
        
        performBulkAction(action) {
            const formData = new FormData();
            formData.append('action', action);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
            
            if (action === 'bulk_reject') {
                formData.append('rejection_reason', this.rejectionReason);
            }
            
            this.selectedRequests.forEach(id => {
                formData.append('selected_requests', id);
            });
            
            fetch('{% url "supply:batch_operations" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
            .then(response => {
                if (response.ok) {
                    // Show success notification
                    const actionText = action === 'bulk_approve' ? 'approved' : 'rejected';
                    showNotification('success', 'Batch Operation Complete', `Successfully ${actionText} ${this.selectedRequests.length} request(s).`);

                    // Refresh the request list
                    htmx.trigger('#request-list', 'refresh');
                    this.clearSelection();
                } else {
                    throw new Error('Server error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('error', 'Operation Failed', 'An error occurred while processing the batch operation.');
            });
        }
    }
}
</script>
{% endblock %}