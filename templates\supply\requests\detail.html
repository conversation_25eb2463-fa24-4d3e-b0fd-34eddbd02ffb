{% extends 'base.html' %}

{% block title %}Request {{ request.request_id }} - MSRRMS{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <div class="flex justify-between items-start">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Request {{ request.request_id }}</h1>
                    <p class="mt-1 text-sm text-gray-600">Submitted on {{ request.created_at|date:"F d, Y \a\t g:i A" }}</p>
                </div>
                <div>
                    {% if request.status == 'PENDING' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <svg class="-ml-1 mr-1.5 h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3" />
                            </svg>
                            Pending Review
                        </span>
                    {% elif request.status == 'APPROVED' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            <svg class="-ml-1 mr-1.5 h-4 w-4 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3" />
                            </svg>
                            Approved
                        </span>
                    {% elif request.status == 'REJECTED' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <svg class="-ml-1 mr-1.5 h-4 w-4 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3" />
                            </svg>
                            Rejected
                        </span>
                    {% elif request.status == 'RELEASED' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <svg class="-ml-1 mr-1.5 h-4 w-4 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3" />
                            </svg>
                            Released
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Request Details -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Request Details</h2>
                        
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Item Requested</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.item.name }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Quantity</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.quantity }} {{ request.item.unit }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Department</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.department }}</dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requested By</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.requester.get_full_name|default:request.requester.username }}</dd>
                            </div>
                            
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500">Purpose</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.purpose }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Item Information -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Item Information</h2>
                        
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Current Stock</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    <span class="{% if request.item.current_stock <= request.item.minimum_stock %}text-red-600 font-semibold{% endif %}">
                                        {{ request.item.current_stock }} {{ request.item.unit }}
                                    </span>
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Minimum Stock</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.item.minimum_stock }} {{ request.item.unit }}</dd>
                            </div>
                            
                            {% if request.item.description %}
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.item.description }}</dd>
                            </div>
                            {% endif %}
                        </dl>
                    </div>
                </div>

                <!-- Approval/Rejection Details -->
                {% if request.status != 'PENDING' %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">
                            {% if request.status == 'REJECTED' %}
                                Rejection Details
                            {% else %}
                                Approval Details
                            {% endif %}
                        </h2>
                        
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">
                                    {% if request.status == 'REJECTED' %}Rejected By{% else %}Approved By{% endif %}
                                </dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    {{ request.approved_by.get_full_name|default:request.approved_by.username }}
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Date</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.approved_at|date:"F d, Y \a\t g:i A" }}</dd>
                            </div>
                            
                            {% if request.approval_remarks %}
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500">Remarks</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.approval_remarks }}</dd>
                            </div>
                            {% endif %}
                        </dl>
                    </div>
                </div>
                {% endif %}

                <!-- Release Details -->
                {% if request.status == 'RELEASED' %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Release Details</h2>
                        
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Released By</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    {{ request.released_by.get_full_name|default:request.released_by.username }}
                                </dd>
                            </div>
                            
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Release Date</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.released_at|date:"F d, Y \a\t g:i A" }}</dd>
                            </div>
                            
                            {% if request.release_remarks %}
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500">Release Remarks</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ request.release_remarks }}</dd>
                            </div>
                            {% endif %}
                        </dl>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Status Timeline -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Status Timeline</h2>
                        
                        <div class="flow-root">
                            <ul class="-mb-8">
                                <!-- Submitted -->
                                <li>
                                    <div class="relative pb-8">
                                        <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></div>
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5">
                                                <div>
                                                    <p class="text-sm text-gray-500">Request submitted</p>
                                                    <p class="text-xs text-gray-400">{{ request.created_at|date:"M d, Y \a\t g:i A" }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <!-- Approved/Rejected -->
                                {% if request.approved_at %}
                                <li>
                                    <div class="relative pb-8">
                                        {% if request.status != 'PENDING' %}
                                            <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></div>
                                        {% endif %}
                                        <div class="relative flex space-x-3">
                                            <div>
                                                {% if request.status == 'REJECTED' %}
                                                    <span class="h-8 w-8 rounded-full bg-red-500 flex items-center justify-center ring-8 ring-white">
                                                        <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                        </svg>
                                                    </span>
                                                {% else %}
                                                    <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                        <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                        </svg>
                                                    </span>
                                                {% endif %}
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5">
                                                <div>
                                                    <p class="text-sm text-gray-500">
                                                        Request {% if request.status == 'REJECTED' %}rejected{% else %}approved{% endif %}
                                                    </p>
                                                    <p class="text-xs text-gray-400">{{ request.approved_at|date:"M d, Y \a\t g:i A" }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {% endif %}

                                <!-- Released -->
                                {% if request.released_at %}
                                <li>
                                    <div class="relative">
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5">
                                                <div>
                                                    <p class="text-sm text-gray-500">Supplies released</p>
                                                    <p class="text-xs text-gray-400">{{ request.released_at|date:"M d, Y \a\t g:i A" }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Actions</h2>
                        
                        <div class="space-y-3">
                            <a href="{% url 'supply:request_history' %}" 
                               class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Back to Requests
                            </a>
                            
                            {% if user.userprofile.role != 'GSO' and request.status == 'PENDING' %}
                            <div class="text-center">
                                <p class="text-xs text-gray-500">
                                    Your request is being reviewed by GSO staff.
                                </p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-refresh for real-time status updates -->
<div hx-get="{% url 'supply:request_status_update' %}?request_ids={{ request.id }}" 
     hx-trigger="every 30s" 
     hx-target="#status-container" 
     hx-swap="innerHTML">
</div>
{% endblock %}