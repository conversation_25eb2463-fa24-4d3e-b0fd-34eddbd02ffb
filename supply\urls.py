from django.urls import path
from . import views

app_name = 'supply'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('register/', views.register, name='register'),
    path('profile/', views.profile, name='profile'),
    path('gso/', views.gso_dashboard, name='gso_dashboard'),
    path('gso/requests/<int:request_id>/', views.gso_request_detail, name='gso_request_detail'),
    path('gso/requests/<int:request_id>/approve/', views.approve_request, name='approve_request'),
    path('gso/requests/<int:request_id>/reject/', views.reject_request, name='reject_request'),
    path('gso/requests/<int:request_id>/release/', views.release_request, name='release_request'),
    path('gso/batch-operations/', views.batch_operations, name='batch_operations'),
    path('gso/releases/', views.release_management, name='release_management'),
    
    # Supply Request URLs
    path('requests/', views.request_history, name='request_history'),
    path('requests/create/', views.request_create, name='request_create'),
    path('requests/<int:request_id>/', views.request_detail, name='request_detail'),
    path('requests/item-info/', views.item_info, name='item_info'),
    path('requests/status-update/', views.request_status_update, name='request_status_update'),
    
    # Inventory URLs
    path('inventory/', views.inventory, name='inventory'),
    path('inventory/add/', views.inventory_add, name='inventory_add'),
    path('inventory/<int:item_id>/', views.inventory_detail, name='inventory_detail'),
    path('inventory/<int:item_id>/edit/', views.inventory_edit, name='inventory_edit'),
    path('inventory/<int:item_id>/adjust/', views.inventory_adjust, name='inventory_adjust'),
    path('inventory/<int:item_id>/delete/', views.inventory_delete, name='inventory_delete'),
    path('inventory/low-stock/', views.low_stock_alerts, name='low_stock_alerts'),
    path('inventory/transactions/', views.inventory_transactions, name='inventory_transactions'),
    
    # Reports URLs
    path('reports/', views.reports, name='reports'),
    path('reports/requests/', views.requests_report, name='requests_report'),
    path('reports/departmental/', views.departmental_usage_report, name='departmental_usage_report'),
    path('reports/inventory/', views.inventory_report, name='inventory_report'),
]
