<!-- Batch Operation Success Message -->
<div class="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">
                {% if action == 'bulk_approve' %}
                    Bulk Approval Completed
                {% elif action == 'bulk_reject' %}
                    Bulk Rejection Completed
                {% endif %}
            </h3>
            <div class="mt-2 text-sm text-green-700">
                <p>
                    {% if action == 'bulk_approve' %}
                        Successfully approved {{ processed_count }} request(s).
                    {% elif action == 'bulk_reject' %}
                        Successfully rejected {{ processed_count }} request(s).
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Trigger refresh of the request list -->
<script>
    // Refresh the request list after batch operation
    htmx.trigger('#request-list', 'refresh');
</script>