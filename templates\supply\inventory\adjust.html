{% extends 'base.html' %}

{% block title %}Adjust Stock - {{ item.name }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="flex items-center mb-6">
            <a href="{% url 'supply:inventory_detail' item.id %}" 
               class="text-blue-600 hover:text-blue-800 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-gray-900">Adjust Stock - {{ item.name }}</h1>
        </div>

        <!-- Current Stock Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Current Stock Information</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p><strong>Current Stock:</strong> {{ item.current_stock }} {{ item.unit }}</p>
                        <p><strong>Minimum Stock:</strong> {{ item.minimum_stock }} {{ item.unit }}</p>
                        {% if item.is_low_stock %}
                        <p class="text-red-600 font-semibold">⚠️ This item is currently below minimum stock level!</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <form method="post" class="space-y-6" x-data="{ adjustmentType: '{{ form.adjustment_type.value|default:'IN' }}' }">
                {% csrf_token %}
                
                <div>
                    <label for="{{ form.adjustment_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Adjustment Type *
                    </label>
                    <select name="{{ form.adjustment_type.name }}" 
                            id="{{ form.adjustment_type.id_for_label }}"
                            x-model="adjustmentType"
                            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm">
                        {% for value, label in form.adjustment_type.field.choices %}
                        <option value="{{ value }}" {% if form.adjustment_type.value == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                    {% if form.adjustment_type.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.adjustment_type.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        <span x-show="adjustmentType === 'IN'">Quantity to Add *</span>
                        <span x-show="adjustmentType === 'OUT'">Quantity to Remove *</span>
                        <span x-show="adjustmentType === 'ADJUSTMENT'">New Total Stock *</span>
                    </label>
                    {{ form.quantity }}
                    {% if form.quantity.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.quantity.errors.0 }}
                        </div>
                    {% endif %}
                    <div class="mt-1 text-sm text-gray-500">
                        <p x-show="adjustmentType === 'IN'">Enter the number of {{ item.unit }} to add to current stock</p>
                        <p x-show="adjustmentType === 'OUT'">Enter the number of {{ item.unit }} to remove from current stock (max: {{ item.current_stock }})</p>
                        <p x-show="adjustmentType === 'ADJUSTMENT'">Enter the new total stock level in {{ item.unit }}</p>
                    </div>
                </div>

                <div>
                    <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Remarks *
                    </label>
                    {{ form.remarks }}
                    {% if form.remarks.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.remarks.errors.0 }}
                        </div>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">Provide a reason for this stock adjustment</p>
                </div>

                <!-- Preview Section -->
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4" 
                     x-data="{ quantity: {{ form.quantity.value|default:0 }}, currentStock: {{ item.current_stock }} }"
                     x-init="$watch('quantity', value => quantity = parseInt(value) || 0)">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">Preview</h3>
                    <div class="text-sm text-gray-600">
                        <p>Current Stock: {{ item.current_stock }} {{ item.unit }}</p>
                        <p x-show="adjustmentType === 'IN'">
                            After adding <span x-text="quantity"></span> {{ item.unit }}: 
                            <span x-text="currentStock + quantity" class="font-semibold"></span> {{ item.unit }}
                        </p>
                        <p x-show="adjustmentType === 'OUT'">
                            After removing <span x-text="quantity"></span> {{ item.unit }}: 
                            <span x-text="Math.max(0, currentStock - quantity)" class="font-semibold"></span> {{ item.unit }}
                        </p>
                        <p x-show="adjustmentType === 'ADJUSTMENT'">
                            New stock level: <span x-text="quantity" class="font-semibold"></span> {{ item.unit }}
                        </p>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-6">
                    <a href="{% url 'supply:inventory_detail' item.id %}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Apply Adjustment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}