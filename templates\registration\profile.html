{% extends 'base.html' %}

{% block title %}Profile - MSRRMS{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">
                User Profile
            </h3>
            
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- First Name -->
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            First Name
                        </label>
                        <div class="mt-1">
                            {{ form.first_name }}
                        </div>
                        {% if form.first_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.first_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Last Name
                        </label>
                        <div class="mt-1">
                            {{ form.last_name }}
                        </div>
                        {% if form.last_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.last_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Email -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Email Address
                    </label>
                    <div class="mt-1">
                        {{ form.email }}
                    </div>
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.email.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Role (Read-only) -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        Role
                    </label>
                    <div class="mt-1">
                        <input type="text" 
                               value="{{ user.userprofile.get_role_display }}" 
                               readonly
                               class="appearance-none relative block w-full px-3 py-2 border border-gray-300 bg-gray-50 text-gray-500 rounded-md focus:outline-none sm:text-sm">
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Role cannot be changed. Contact administrator if needed.</p>
                </div>

                <!-- Department -->
                <div>
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Department
                    </label>
                    <div class="mt-1">
                        {{ form.department }}
                    </div>
                    {% if form.department.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.department.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Phone -->
                <div>
                    <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Phone Number
                    </label>
                    <div class="mt-1">
                        {{ form.phone }}
                    </div>
                    {% if form.phone.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.phone.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Account Information -->
                <div class="bg-gray-50 px-4 py-3 rounded-md">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Account Information</h4>
                    <div class="text-sm text-gray-600 space-y-1">
                        <p><strong>Username:</strong> {{ user.username }}</p>
                        <p><strong>Date Joined:</strong> {{ user.date_joined|date:"F d, Y" }}</p>
                        <p><strong>Profile Created:</strong> {{ user.userprofile.created_at|date:"F d, Y" }}</p>
                    </div>
                </div>

                {% if form.non_field_errors %}
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {{ form.non_field_errors }}
                </div>
                {% endif %}

                <div class="flex justify-end space-x-3">
                    <a href="{% url 'supply:dashboard' %}" 
                       class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Update Profile
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}